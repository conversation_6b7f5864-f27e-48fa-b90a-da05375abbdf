#!/usr/bin/env node

/**
 * Next.js VSCode 调试故障排除脚本
 * 用于诊断和修复常见的调试配置问题
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Next.js VSCode 调试故障排除工具\n');

// 检查端口占用情况
function checkPortUsage() {
  console.log('📡 检查端口占用情况...');
  
  const ports = [3000, 9229, 9230, 9222];
  
  ports.forEach(port => {
    try {
      // Windows 命令检查端口
      const result = execSync(`netstat -ano | findstr :${port}`, { encoding: 'utf8' });
      if (result.trim()) {
        console.log(`⚠️  端口 ${port} 被占用:`);
        console.log(result.trim());
      } else {
        console.log(`✅ 端口 ${port} 可用`);
      }
    } catch (error) {
      console.log(`✅ 端口 ${port} 可用`);
    }
  });
  console.log('');
}

// 检查 VSCode 调试配置
function checkVSCodeConfig() {
  console.log('🔧 检查 VSCode 调试配置...');
  
  const launchJsonPath = path.join(process.cwd(), '.vscode', 'launch.json');
  
  if (!fs.existsSync(launchJsonPath)) {
    console.log('❌ 未找到 .vscode/launch.json 文件');
    return false;
  }
  
  try {
    const launchConfig = JSON.parse(fs.readFileSync(launchJsonPath, 'utf8'));
    
    // 检查必要的配置
    const requiredConfigs = [
      'Next.js: debug server-side',
      'Next.js: debug client-side',
      'Next.js: debug full stack'
    ];
    
    const existingConfigs = launchConfig.configurations.map(c => c.name);
    
    requiredConfigs.forEach(configName => {
      if (existingConfigs.includes(configName)) {
        console.log(`✅ 找到配置: ${configName}`);
      } else {
        console.log(`❌ 缺少配置: ${configName}`);
      }
    });
    
    console.log('');
    return true;
  } catch (error) {
    console.log('❌ launch.json 格式错误:', error.message);
    return false;
  }
}

// 检查 package.json 脚本
function checkPackageScripts() {
  console.log('📦 检查 package.json 调试脚本...');
  
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    console.log('❌ 未找到 package.json 文件');
    return false;
  }
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    const requiredScripts = ['dev:debug', 'dev:debug-brk'];
    
    requiredScripts.forEach(scriptName => {
      if (packageJson.scripts && packageJson.scripts[scriptName]) {
        console.log(`✅ 找到脚本: ${scriptName}`);
        console.log(`   命令: ${packageJson.scripts[scriptName]}`);
      } else {
        console.log(`❌ 缺少脚本: ${scriptName}`);
      }
    });
    
    // 检查 cross-env 依赖
    const hasCrossEnv = 
      (packageJson.dependencies && packageJson.dependencies['cross-env']) ||
      (packageJson.devDependencies && packageJson.devDependencies['cross-env']);
    
    if (hasCrossEnv) {
      console.log('✅ 找到 cross-env 依赖');
    } else {
      console.log('❌ 缺少 cross-env 依赖');
      console.log('   请运行: npm install --save-dev cross-env');
    }
    
    console.log('');
    return true;
  } catch (error) {
    console.log('❌ package.json 格式错误:', error.message);
    return false;
  }
}

// 测试调试服务器启动
function testDebugServer() {
  console.log('🚀 测试调试服务器启动...');
  
  return new Promise((resolve) => {
    const child = spawn('npm', ['run', 'dev:debug'], {
      stdio: 'pipe',
      shell: true
    });
    
    let output = '';
    let hasStarted = false;
    
    const timeout = setTimeout(() => {
      if (!hasStarted) {
        console.log('❌ 服务器启动超时');
        child.kill();
        resolve(false);
      }
    }, 15000);
    
    child.stdout.on('data', (data) => {
      output += data.toString();
      
      if (output.includes('Ready in') || output.includes('started server')) {
        console.log('✅ 调试服务器启动成功');
        console.log('✅ 检测到调试端口信息');
        hasStarted = true;
        clearTimeout(timeout);
        child.kill();
        resolve(true);
      }
      
      if (output.includes('Debugger listening')) {
        console.log('✅ Node.js 调试器已启动');
      }
    });
    
    child.stderr.on('data', (data) => {
      const error = data.toString();
      if (error.includes('EADDRINUSE')) {
        console.log('❌ 端口被占用，请先关闭其他 Next.js 进程');
        clearTimeout(timeout);
        child.kill();
        resolve(false);
      }
    });
    
    child.on('error', (error) => {
      console.log('❌ 启动失败:', error.message);
      clearTimeout(timeout);
      resolve(false);
    });
  });
}

// 提供解决方案
function provideSolutions() {
  console.log('💡 常见问题解决方案:\n');
  
  console.log('1. 如果端口被占用:');
  console.log('   - 关闭其他 Next.js 进程');
  console.log('   - 或者修改 package.json 中的端口号\n');
  
  console.log('2. 如果浏览器无法附加:');
  console.log('   - 先启动 "Next.js: debug server-side"');
  console.log('   - 等服务器完全启动后再启动客户端调试');
  console.log('   - 或者手动打开浏览器访问 http://localhost:3000\n');
  
  console.log('3. 如果断点不生效:');
  console.log('   - 清除 .next 缓存目录');
  console.log('   - 重启 VSCode');
  console.log('   - 检查源映射配置\n');
  
  console.log('4. 推荐的调试步骤:');
  console.log('   ① npm run dev:debug');
  console.log('   ② 在 VSCode 中选择 "Next.js: debug server-side"');
  console.log('   ③ 按 F5 启动服务端调试');
  console.log('   ④ 手动打开浏览器访问 http://localhost:3000');
  console.log('   ⑤ 在 VSCode 中选择 "Next.js: debug client-side"');
  console.log('   ⑥ 按 F5 启动客户端调试');
}

// 主函数
async function main() {
  checkPortUsage();
  checkVSCodeConfig();
  checkPackageScripts();
  
  console.log('🧪 是否要测试调试服务器启动? (这将启动服务器并在15秒后自动关闭)');
  console.log('如果要测试，请按 Ctrl+C 取消，然后运行: node debug-troubleshoot.js --test\n');
  
  if (process.argv.includes('--test')) {
    const success = await testDebugServer();
    if (!success) {
      console.log('\n❌ 调试服务器测试失败');
    }
  }
  
  provideSolutions();
}

main().catch(console.error);
