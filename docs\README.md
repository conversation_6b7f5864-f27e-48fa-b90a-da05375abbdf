# RefundGo Documentation

Welcome to the comprehensive documentation for RefundGo, a multilingual task publishing and completion platform built with Next.js 14+ and modern React patterns.

## 📚 Documentation Structure

### 🚀 Getting Started

- **[Project Overview](project-context.md)** - Application purpose, features, and tech stack
- **[Quick Setup Guide](getting-started/vscode-setup.md)** - VSCode configuration and development tools
- **[Code Quality Standards](getting-started/eslint-configuration.md)** - ESLint configuration and standards

### 🏗️ Architecture & Design

- **[System Architecture](architecture/system-architecture.md)** - Technical architecture and design patterns
- **[Database Schema](project-context.md#database-schema-prisma)** - Data models and relationships
- **[API Reference](project-context.md#api-endpoints)** - API endpoints and patterns
- **[User Roles](architecture/user-roles.md)** - User permissions and access control
- **[Feature Modules](architecture/feature-modules.md)** - Modular development approach

### 🔧 Development

- **[Development Guide](development-guide.md)** - Setup, patterns, and best practices
- **[Code Quality Standards](getting-started/eslint-configuration.md)** - ESLint configuration and coding standards
- **[Environment Setup](getting-started/vscode-setup.md)** - VSCode configuration and development tools

### 🌍 Features & Integrations

- **[Internationalization](features/internationalization.md)** - Complete i18n implementation guide
- **[Email System](features/email-system.md)** - Email templates, notifications, and language detection
- **[Payment System](features/currency-conversion.md)** - Payment processing and currency handling
- **[Authentication](project-context.md#authentication)** - NextAuth v5 implementation
- **[Cron Jobs](features/cron-job-setup.md)** - Scheduled tasks and automation
- **[i18n Ally Setup](features/i18n-ally-setup-guide.md)** - Translation management tooling
- **[YunPay Integration](features/yunpay-php-sdk-analysis.md)** - Payment provider analysis and integration

### 🎨 UI Components & Design

- **[Component Library](ui-components/component-library.md)** - Complete component documentation and patterns
- **[Responsive Design](ui-components/responsive-design.md)** - Mobile-first responsive implementation
- **[Dark Mode & Theming](ui-components/DARK_MODE_TEXT_VISIBILITY_FIX.md)** - Dark mode implementation and text visibility
- **[Navigation Components](ui-components/NAVBAR_OVERFLOW_FIX.md)** - Navigation fixes and overflow handling
- **[Footer Components](ui-components/FOOTER_THEME_TOGGLE_FIX.md)** - Footer theme integration
- **[Layout Consistency](ui-components/layout-width-consistency-fix.md)** - Container width standardization
- **[Membership Cards](ui-components/MEMBERSHIP_CARD_BORDER_RADIUS_FIX.md)** - Card styling improvements

### 🚀 Deployment & Operations

- **[Production Deployment](project-context.md#deployment)** - Build and deployment process
- **[Environment Configuration](project-context.md#environment-variables)** - Environment variables
- **[Monitoring](project-context.md#monitoring-and-operations)** - Error tracking and performance
- **[Deployment Guides](deployment/)** - Detailed deployment documentation (coming soon)

### 📋 Implementation History

- **[Recent Fixes](changelog/recent-fixes.md)** - Consolidated bug fixes and improvements
- **[Logo Implementation](changelog/LOGO_FIXES_SUMMARY.md)** - Logo system fixes and enhancements
- **[Footer Redesign](changelog/footer-final-summary.md)** - Footer simplification and improvements
- **[CSS Refactoring](changelog/css-refactoring-summary.md)** - Performance improvements and optimization
- **[Email Enhancements](changelog/email-system-implementation-summary.md)** - Email system improvements
- **[Homepage Redesign](changelog/homepage-redesign-summary.md)** - Homepage layout and styling updates
- **[Login System](changelog/login-redesign-completion-report.md)** - Authentication UI improvements
- **[Payment Fixes](changelog/yunpay-amount-mismatch-fix.md)** - YunPay integration fixes
- **[Logo Updates](changelog/HOMEPAGE_LOGO_UPDATE.md)** - Homepage logo implementation
- **[Logo Components](changelog/LOGO_IMPLEMENTATION.md)** - Logo component development

### 🔬 Detailed Implementation

- **[Responsive Tabs](implementation/COMPREHENSIVE_RESPONSIVE_TABS_IMPLEMENTATION.md)** - Complete responsive tab system
- **[i18n Tab Restoration](implementation/I18N_RESPONSIVE_TABS_RESTORATION.md)** - Internationalization for responsive tabs
- **[Task Tab Implementation](implementation/RESPONSIVE_TASK_TABS_IMPLEMENTATION.md)** - Task-specific responsive tabs

### 🧪 Testing & Quality

- **[Testing Guide](testing-guide.md)** - Comprehensive testing strategies and examples
- **[Translation Testing](testing/test-translation.md)** - i18n testing approaches

### 📚 Additional Resources

- **[User Interaction Guide](user-interaction-guide.md)** - User experience patterns and guidelines
- **[AI Assistant Guide](ai-assistant-guide.md)** - AI integration and usage patterns
- **[Reorganization Summary](REORGANIZATION_SUMMARY.md)** - Documentation restructuring details

## 🔍 Quick Navigation

### For New Developers

1. Start with [Project Overview](project-context.md)
2. Follow [Development Guide](development-guide.md) for setup
3. Configure [Environment Setup](getting-started/vscode-setup.md)
4. Review [Testing Guide](testing-guide.md) for quality standards

### For Feature Development

1. Check [System Architecture](architecture/system-architecture.md) for overall design
2. Review [Feature Modules](architecture/feature-modules.md) for modular architecture
3. Study [API Reference](project-context.md#api-endpoints) for endpoints
4. Follow [Code Quality Standards](getting-started/eslint-configuration.md)

### For UI/UX Work

1. Review [Component Library](ui-components/component-library.md) for design system
2. Study [Responsive Design](ui-components/responsive-design.md) for mobile patterns
3. Check [Dark Mode Implementation](ui-components/DARK_MODE_TEXT_VISIBILITY_FIX.md) for theming
4. Follow [User Interaction Guide](user-interaction-guide.md) for UX patterns

### For Internationalization

1. Read [Internationalization Guide](features/internationalization.md) for i18n implementation
2. Setup [i18n Ally Tools](features/i18n-ally-setup-guide.md) for translation management
3. Review [Translation Testing](testing/test-translation.md) for quality assurance

### For Deployment

1. Review [Environment Configuration](project-context.md#environment-variables)
2. Follow [Production Deployment](project-context.md#deployment) guide
3. Set up [Monitoring](project-context.md#monitoring-and-operations)

## 🛠️ Key Technologies

- **Framework**: Next.js 14+ (App Router)
- **Language**: TypeScript 5.8+ (strict mode)
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth v5 (beta)
- **UI**: Tailwind CSS, shadcn/ui, Radix UI
- **State Management**: TanStack Query, Zustand
- **Internationalization**: next-intl
- **Testing**: Jest, React Testing Library, Playwright

## 📖 Documentation Standards

### File Organization

- Core documentation in root `/docs` folder
- Getting started guides in `/docs/getting-started/`
- Architecture documentation in `/docs/architecture/`
- Feature-specific docs in `/docs/features/`
- UI component docs in `/docs/ui-components/`
- Implementation details in `/docs/implementation/`
- Testing documentation in `/docs/testing/`
- Change history in `/docs/changelog/`
- Deployment guides in `/docs/deployment/`
- Archive old content in `/docs/archive-old/`

### Naming Conventions

- Use kebab-case for file names
- Include descriptive prefixes (e.g., `api-`, `ui-`, `fix-`)
- Use `.md` extension for all documentation

### Content Guidelines

- Start with clear overview and objectives
- Include code examples where relevant
- Maintain consistent formatting
- Update modification dates
- Cross-reference related documents

## 🔗 External Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [Prisma Documentation](https://www.prisma.io/docs)
- [NextAuth.js](https://authjs.dev/)
- [next-intl](https://next-intl-docs.vercel.app/)

## 📝 Contributing to Documentation

1. Follow the established structure and naming conventions
2. Update this README when adding new documentation
3. Ensure all internal links are functional
4. Include relevant code examples and implementation details
5. Maintain consistent formatting and style

---

**Last Updated**: 2025-01-29  
**Documentation Version**: 2.0  
**Project Version**: RefundGo Web 2.0
